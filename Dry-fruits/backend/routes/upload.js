const express = require('express');
const multer = require('multer');
const cloudinary = require('cloudinary').v2;
const path = require('path');
const fs = require('fs');
const { adminAuth } = require('../middleware/auth');

const router = express.Router();

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

// Use local storage for now
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    // Generate a clean filename without special characters
    const timestamp = Date.now();
    const extension = file.originalname.split('.').pop();
    const cleanFilename = `${timestamp}-image.${extension}`;
    cb(null, cleanFilename);
  }
});

// File filter to only allow images
const fileFilter = (req, file, cb) => {
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: fileFilter
});

// @route   POST /api/upload/image
// @desc    Upload product image
// @access  Private/Admin
router.post('/image', adminAuth, upload.single('image'), (req, res) => {
  try {
    console.log('Upload request received:', {
      hasFile: !!req.file,
      fileInfo: req.file ? {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        path: req.file.path,
        filename: req.file.filename
      } : null
    });

    if (!req.file) {
      console.log('No file provided in request');
      return res.status(400).json({ message: 'No image file provided' });
    }

    // Local storage returns the file path
    const imageUrl = `/uploads/${req.file.filename}`;
    const publicId = req.file.filename;
    
    console.log('Upload successful:', { imageUrl, publicId });
    
    res.json({
      success: true,
      message: 'Image uploaded successfully',
      imageUrl: imageUrl,
      publicId: publicId,
      cloudinaryId: publicId
    });
  } catch (error) {
    console.error('Upload error:', error);
    console.error('Error details:', error.message);
    console.error('Error stack:', error.stack);
    res.status(500).json({ 
      message: 'Server error uploading image',
      error: error.message 
    });
  }
});

// @route   POST /api/upload/images
// @desc    Upload multiple product images to Cloudinary
// @access  Private/Admin
router.post('/images', adminAuth, upload.array('images', 5), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ message: 'No image files provided' });
    }

    const imageUrls = req.files.map(file => ({
      url: file.path, // Cloudinary URL
      alt: file.originalname.split('.')[0], // Use filename as alt text
      publicId: file.filename // Cloudinary public ID
    }));
    
    res.json({
      success: true,
      message: 'Images uploaded successfully to Cloudinary',
      images: imageUrls
    });
  } catch (error) {
    console.error('Cloudinary images upload error:', error);
    res.status(500).json({ message: 'Server error uploading images to Cloudinary' });
  }
});

// @route   DELETE /api/upload/image/:publicId
// @desc    Delete uploaded image from Cloudinary
// @access  Private/Admin
router.delete('/image/:publicId', adminAuth, async (req, res) => {
  try {
    const publicId = req.params.publicId;
    
    // Delete from Cloudinary
    const result = await cloudinary.uploader.destroy(publicId);
    
    if (result.result === 'ok') {
      res.json({
        success: true,
        message: 'Image deleted successfully from Cloudinary'
      });
    } else {
      res.status(404).json({ message: 'Image not found in Cloudinary' });
    }
  } catch (error) {
    console.error('Cloudinary delete error:', error);
    res.status(500).json({ message: 'Server error deleting image from Cloudinary' });
  }
});

module.exports = router;
