{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev-mock": "nodemon dev-server.js", "seed": "node seeders/seedDatabase.js", "seed-products": "node seeders/seedProducts.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^1.41.3", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.2", "multer": "^2.0.2", "multer-storage-cloudinary": "^4.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}}