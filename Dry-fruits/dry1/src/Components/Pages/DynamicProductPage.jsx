import React, { useState, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';
import Header from '../Homepages/Header.jsx';
import Navbar from '../Homepages/Navbar.jsx';
import Footer from '../Homepages/Footer.jsx';
import YouMayAlsoLike from '../Homepages/YouMayAlsoLike.jsx';
import { productsAPI } from '../../services/api.js';
import { useNotification } from '../Common/NotificationProvider.jsx';
import config from '../../config/environment.js';
import { getImageUrl } from '../../utils/urls.js';

const DynamicProductPage = () => {
  const { slug } = useParams();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  const [sortBy, setSortBy] = useState('featured');
  const [priceRange, setPriceRange] = useState([0, 2000]);
  const [selectedSizes, setSelectedSizes] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const { showSuccess, showError } = useNotification();

  useEffect(() => {
    fetchProducts();
  }, [slug]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      // Try to fetch products by category slug
      const response = await productsAPI.getAll({ 
        category: slug,
        limit: 100 
      });
      setProducts(response.products || []);
    } catch (error) {
      console.error('Error fetching products:', error);
      showError('Failed to load products');
      // Use fallback data
      setProducts(getFallbackProducts());
    } finally {
      setLoading(false);
    }
  };

  const getFallbackProducts = () => {
    // Fallback products based on slug
    const fallbackData = {
      'macadamia-nuts': [
        {
          _id: 'fallback-macadamia-1',
          name: 'Premium Macadamia Nuts',
          images: [{ url: '/dry.png', alt: 'Premium Macadamia Nuts' }],
          sizes: [{ size: '250g', price: 899, originalPrice: 1199, stock: 20 }],
          rating: { average: 4.8, count: 45 },
          badges: [{ text: 'Premium', color: 'green' }],
          isBestSeller: true
        }
      ],
      'hazelnuts': [
        {
          _id: 'fallback-hazelnuts-1',
          name: 'Premium Hazelnuts',
          images: [{ url: '/dry.png', alt: 'Premium Hazelnuts' }],
          sizes: [{ size: '250g', price: 699, originalPrice: 899, stock: 25 }],
          rating: { average: 4.6, count: 38 },
          badges: [{ text: 'Popular', color: 'blue' }],
          isBestSeller: false
        }
      ]
    };
    
    return fallbackData[slug] || [];
  };

  const handleSortChange = (sortType) => {
    setSortBy(sortType);
    // Implement sorting logic
    let sortedProducts = [...products];
    
    switch (sortType) {
      case 'price-low':
        sortedProducts.sort((a, b) => (a.sizes[0]?.price || 0) - (b.sizes[0]?.price || 0));
        break;
      case 'price-high':
        sortedProducts.sort((a, b) => (b.sizes[0]?.price || 0) - (a.sizes[0]?.price || 0));
        break;
      case 'rating':
        sortedProducts.sort((a, b) => (b.rating?.average || 0) - (a.rating?.average || 0));
        break;
      case 'newest':
        sortedProducts.sort((a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0));
        break;
      default:
        // Featured - keep original order
        break;
    }
    
    setProducts(sortedProducts);
  };

  const handleSizeFilter = (size) => {
    setSelectedSizes(prev => 
      prev.includes(size) 
        ? prev.filter(s => s !== size)
        : [...prev, size]
    );
  };

  const filteredProducts = products.filter(product => {
    const matchesPrice = product.sizes.some(size => 
      size.price >= priceRange[0] && size.price <= priceRange[1]
    );
    
    const matchesSize = selectedSizes.length === 0 || 
      product.sizes.some(size => selectedSizes.includes(size.size));
    
    const matchesSearch = searchTerm === '' || 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesPrice && matchesSize && matchesSearch;
  });

  const getPageTitle = () => {
    const titleMap = {
      'macadamia-nuts': 'Macadamia Nuts',
      'hazelnuts': 'Hazelnuts',
      'pecans': 'Pecans',
      'pine-nuts': 'Pine Nuts'
    };
    return titleMap[slug] || slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <Navbar />
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-gray-600">Loading products...</div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <Navbar />
      
      {/* Page Header */}
      <div className="bg-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {getPageTitle()}
          </h1>
          <p className="text-gray-600">
            Discover our premium selection of {getPageTitle().toLowerCase()}
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <div className="lg:w-64 space-y-6">
            {/* Search */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-3">Search</h3>
              <input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
              />
            </div>

            {/* Price Range */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-3">Price Range</h3>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="2000"
                  value={priceRange[1]}
                  onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-600">
                  <span>₹{priceRange[0]}</span>
                  <span>₹{priceRange[1]}</span>
                </div>
              </div>
            </div>

            {/* Size Filter */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-3">Size</h3>
              <div className="space-y-2">
                {['100g', '250g', '500g', '1kg'].map((size) => (
                  <label key={size} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedSizes.includes(size)}
                      onChange={() => handleSizeFilter(size)}
                      className="mr-2"
                    />
                    <span className="text-sm">{size}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Sort Options */}
            <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
              <div className="flex flex-wrap gap-2">
                {[
                  { value: 'featured', label: 'Featured' },
                  { value: 'price-low', label: 'Price: Low to High' },
                  { value: 'price-high', label: 'Price: High to Low' },
                  { value: 'rating', label: 'Customer Rating' },
                  { value: 'newest', label: 'Newest' }
                ].map((option) => (
                  <button
                    key={option.value}
                    onClick={() => handleSortChange(option.value)}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                      sortBy === option.value
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Products Grid */}
            {filteredProducts.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredProducts.map((product) => {
                  const mainSize = product.sizes && product.sizes.length > 0 ? product.sizes[0] : null;
                  const mainBadge = product.badges && product.badges.length > 0 ? product.badges[0] : null;
                  const discountPercentage = mainSize && mainSize.originalPrice ? 
                    Math.round(((mainSize.originalPrice - mainSize.price) / mainSize.originalPrice) * 100) : 0;
                  
                  return (
                    <Link 
                      key={product._id} 
                      to={`/product/${product._id}`}
                      className="block bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden"
                    >
                      {/* Badge */}
                      {mainBadge && (
                        <div className="absolute top-3 left-3 z-10">
                          <span className={`bg-${mainBadge.color}-600 text-white px-2 py-1 text-xs font-semibold rounded`}>
                            {mainBadge.text}
                          </span>
                        </div>
                      )}

                      {/* Sale Tag */}
                      {discountPercentage > 0 && (
                        <div className="absolute top-3 right-3 z-10">
                          <span className="bg-green-500 text-white px-2 py-1 text-xs rounded">
                            {discountPercentage}% OFF
                          </span>
                        </div>
                      )}

                      {/* Product Image */}
                      <div className="aspect-[4/3] bg-gray-100 flex items-center justify-center relative overflow-hidden">
                        {product.images && product.images.length > 0 ? (
                          <img 
                            src={getImageUrl(product.images[0].url)} 
                            alt={product.images[0].alt || product.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                            <span className="text-gray-400 text-sm">Product Image</span>
                          </div>
                        )}
                      </div>

                      {/* Product Info */}
                      <div className="p-4">
                        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{product.name}</h3>
                        
                        {mainSize && (
                          <div className="mb-3">
                            <div className="flex items-center space-x-2">
                              <span className="text-lg font-bold text-green-600">₹{mainSize.price}</span>
                              {mainSize.originalPrice && (
                                <span className="text-sm text-gray-500 line-through">₹{mainSize.originalPrice}</span>
                              )}
                            </div>
                            <p className="text-sm text-gray-600">{mainSize.size}</p>
                          </div>
                        )}

                        {/* Rating */}
                        {product.rating && (
                          <div className="flex items-center space-x-1 mb-3">
                            <div className="flex text-yellow-400">
                              {[...Array(5)].map((_, i) => (
                                <svg key={i} className={`w-4 h-4 ${i < Math.floor(product.rating.average) ? 'fill-current' : 'text-gray-300'}`} viewBox="0 0 20 20">
                                  <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                </svg>
                              ))}
                            </div>
                            <span className="text-sm text-gray-600">({product.rating.count})</span>
                          </div>
                        )}

                      </div>
                    </Link>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">No products found</p>
                <p className="text-gray-400 text-sm mt-2">Try adjusting your filters or search terms</p>
              </div>
            )}
          </div>
        </div>
      </div>

      <YouMayAlsoLike />
      <Footer />
    </div>
  );
};

export default DynamicProductPage;
