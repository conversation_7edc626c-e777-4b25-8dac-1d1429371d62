import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../../Homepages/Header.jsx';
import Navbar from '../../Homepages/Navbar.jsx';
import Footer from '../../Homepages/Footer.jsx';
import { useNotification } from '../../Common/NotificationProvider.jsx';
import { ordersAPI } from '../../../services/api.js';

const OrderReview = () => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();
  
  const [cartItems, setCartItems] = useState([]);
  const [shippingAddress, setShippingAddress] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState(null);
  const [orderNote, setOrderNote] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Load cart items from localStorage
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      setCartItems(JSON.parse(savedCart));
    }

    // Load shipping address
    const savedAddress = localStorage.getItem('shippingAddress');
    if (savedAddress) {
      setShippingAddress(JSON.parse(savedAddress));
    }

    // Load payment method
    const savedPayment = localStorage.getItem('paymentMethod');
    if (savedPayment) {
      setPaymentMethod(JSON.parse(savedPayment));
    }
  }, []);

  // Auto-save draft order when user has basic info but hasn't completed payment
  useEffect(() => {
    if (shippingAddress && shippingAddress.phone && cartItems.length > 0) {
      // Debounce the draft save to avoid too many API calls
      const timeoutId = setTimeout(() => {
        saveDraftOrder();
      }, 2000); // Save draft after 2 seconds of inactivity

      return () => clearTimeout(timeoutId);
    }
  }, [shippingAddress, cartItems, paymentMethod, orderNote]);

  const calculateSubtotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const calculateShipping = () => {
    const subtotal = calculateSubtotal();
    return subtotal > 500 ? 0 : 50; // Free shipping above ₹500
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateShipping();
  };

  // Save draft order when user has basic info but hasn't completed payment
  const saveDraftOrder = async () => {
    if (!shippingAddress || !shippingAddress.phone) {
      return; // Don't save if no basic info
    }

    try {
      const draftOrderData = {
        items: cartItems.map(item => ({
          product: item.productId,
          name: item.name,
          size: item.size,
          quantity: item.quantity,
          price: item.price,
          originalPrice: item.originalPrice || item.price,
          image: item.image
        })),
        shippingAddress: {
          name: shippingAddress.name || '',
          phone: shippingAddress.phone,
          email: shippingAddress.email || '',
          street: shippingAddress.address || '',
          city: shippingAddress.city || '',
          state: shippingAddress.state || '',
          pincode: shippingAddress.pincode || '',
          country: shippingAddress.country || 'India'
        },
        paymentMethod: paymentMethod?.method || 'pending',
        paymentDetails: paymentMethod?.data || {},
        orderNote: orderNote,
        subtotal: calculateSubtotal(),
        shipping: calculateShipping(),
        total: calculateTotal()
      };

      // Save draft order to backend
      await ordersAPI.createDraft(draftOrderData);
      console.log('Draft order saved successfully');
    } catch (error) {
      console.error('Error saving draft order:', error);
    }
  };

  const handlePlaceOrder = async () => {
    if (!shippingAddress || !paymentMethod) {
      showError('Please complete address and payment details first');
      return;
    }

    setLoading(true);

    try {
      // Create order object for backend
      const orderData = {
        items: cartItems.map(item => ({
          product: item.productId,
          name: item.name,
          size: item.size,
          quantity: item.quantity,
          price: item.price,
          originalPrice: item.originalPrice || item.price,
          image: item.image
        })),
        shippingAddress: {
          name: shippingAddress.name,
          phone: shippingAddress.phone,
          email: shippingAddress.email,
          street: shippingAddress.address,
          city: shippingAddress.city,
          state: shippingAddress.state,
          pincode: shippingAddress.pincode,
          country: shippingAddress.country || 'India'
        },
        paymentMethod: paymentMethod.method,
        paymentDetails: paymentMethod.data,
        orderNote: orderNote,
        subtotal: calculateSubtotal(),
        shipping: calculateShipping(),
        total: calculateTotal(),
        status: 'pending'
      };

      // Create order in backend
      const response = await ordersAPI.create(orderData);
      
      if (response.success) {
        // Also save to localStorage for offline access
        const localOrder = {
          id: response.order.orderNumber,
          ...orderData,
          createdAt: new Date().toISOString(),
          estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
        };
        
        const existingOrders = JSON.parse(localStorage.getItem('orders') || '[]');
        existingOrders.push(localOrder);
        localStorage.setItem('orders', JSON.stringify(existingOrders));

        // Clear cart
        localStorage.removeItem('cart');
        localStorage.removeItem('shippingAddress');
        localStorage.removeItem('paymentMethod');

        showSuccess('Order placed successfully!');
        
        // Navigate to order confirmation
        navigate(`/order-confirmation/${response.order.orderNumber}`);
      } else {
        showError(response.message || 'Failed to place order');
      }
    } catch (error) {
      console.error('Error placing order:', error);
      showError('Failed to place order. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatPaymentMethod = (method) => {
    if (method.method === 'card') {
      const cardNumber = method.data.cardNumber.replace(/\s/g, '');
      return `**** **** **** ${cardNumber.slice(-4)}`;
    } else if (method.method === 'upi') {
      return method.data.upiId;
    } else if (method.method === 'netbanking') {
      return method.data.bank;
    }
    return 'Unknown';
  };

  if (!shippingAddress || !paymentMethod) {
    return (
      <div className="w-full min-h-screen">
        <Header />
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 md:px-8 py-8">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Missing Information</h1>
            <p className="text-gray-600 mb-6">Please complete your address and payment details first.</p>
            <button
              onClick={() => navigate('/address')}
              className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              Complete Checkout
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen">
      <Header />
      <Navbar />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 md:px-8 py-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                ✓
              </div>
              <span className="ml-2 text-sm font-medium text-green-600">Address</span>
            </div>
            <div className="w-16 h-0.5 bg-green-600"></div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                ✓
              </div>
              <span className="ml-2 text-sm font-medium text-green-600">Payment</span>
            </div>
            <div className="w-16 h-0.5 bg-green-600"></div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                3
              </div>
              <span className="ml-2 text-sm font-medium text-green-600">Review</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Items */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Order Items</h2>
              <div className="space-y-4">
                {cartItems.map((item, index) => (
                  <div key={index} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <img
                      src={item.image || '/dev1.png'}
                      alt={item.name}
                      className="w-16 h-16 object-cover rounded"
                    />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{item.name}</h3>
                      <p className="text-sm text-gray-500">{item.size}</p>
                      <p className="text-sm text-gray-500">Quantity: {item.quantity}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">₹{item.price * item.quantity}</p>
                      <p className="text-sm text-gray-500">₹{item.price} each</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Shipping Address */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Shipping Address</h2>
              <div className="text-gray-700">
                <p className="font-medium">{shippingAddress.firstName} {shippingAddress.lastName}</p>
                <p>{shippingAddress.address}</p>
                <p>{shippingAddress.city}, {shippingAddress.state} {shippingAddress.pincode}</p>
                <p>{shippingAddress.country}</p>
                <p className="mt-2">Phone: {shippingAddress.phone}</p>
                <p>Email: {shippingAddress.email}</p>
              </div>
            </div>

            {/* Payment Method */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Payment Method</h2>
              <div className="text-gray-700">
                <p className="font-medium capitalize">{paymentMethod.method.replace('netbanking', 'Net Banking')}</p>
                <p>{formatPaymentMethod(paymentMethod)}</p>
              </div>
            </div>

            {/* Order Note */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Order Note</h2>
              <textarea
                value={orderNote}
                onChange={(e) => setOrderNote(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                rows={3}
                placeholder="Add any special instructions for your order..."
              />
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-6 sticky top-8">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Order Summary</h2>
              
              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-medium">₹{calculateSubtotal()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span className="font-medium">
                    {calculateShipping() === 0 ? 'Free' : `₹${calculateShipping()}`}
                  </span>
                </div>
                {calculateShipping() === 0 && (
                  <p className="text-sm text-green-600">🎉 You qualify for free shipping!</p>
                )}
                <div className="border-t pt-3">
                  <div className="flex justify-between">
                    <span className="text-lg font-bold text-gray-900">Total</span>
                    <span className="text-lg font-bold text-green-600">₹{calculateTotal()}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <button
                  onClick={handlePlaceOrder}
                  disabled={loading}
                  className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
                    loading
                      ? 'bg-gray-400 text-white cursor-not-allowed'
                      : 'bg-green-600 text-white hover:bg-green-700'
                  }`}
                >
                  {loading ? 'Placing Order...' : 'Place Order'}
                </button>
                
                <button
                  onClick={() => navigate('/payment')}
                  className="w-full py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Back to Payment
                </button>
              </div>

              <div className="mt-6 text-xs text-gray-500">
                <p>By placing this order, you agree to our Terms of Service and Privacy Policy.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default OrderReview;
