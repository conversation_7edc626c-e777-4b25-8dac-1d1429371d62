import React, { useState } from 'react';
import Header from '../../Homepages/Header.jsx';
import Navbar from '../../Homepages/Navbar.jsx';
import Footer from '../../Homepages/Footer.jsx';

const Pistachios = () => {
  const [sortBy, setSortBy] = useState('Featured');
  const [priceRange, setPriceRange] = useState({ min: 0, max: 800 });
  const [selectedSizes, setSelectedSizes] = useState([]);

  const products = [
    {
      id: 1,
      name: "Happilo Premium California Pistachios",
      image: "/dev1.png",
      weight: "200g",
      price: 645,
      originalPrice: 725,
      rating: 4.4,
      reviews: 18,
      badge: "BEST SELLER",
      badgeColor: "bg-red-500"
    },
    {
      id: 2,
      name: "Happilo Roasted & Salted Pistachios",
      image: "/dev2.png",
      weight: "150g",
      price: 485,
      originalPrice: 545,
      rating: 4.6,
      reviews: 24,
      badge: "Popular",
      badgeColor: "bg-orange-500"
    },
    {
      id: 3,
      name: "Happilo Turkish Antep Pistachios",
      image: "/dev1.png",
      weight: "250g",
      price: 795,
      originalPrice: null,
      rating: 4.8,
      reviews: 12,
      badge: "Premium",
      badgeColor: "bg-purple-500"
    },
    {
      id: 4,
      name: "Happilo Raw Pistachios Shelled",
      image: "/dev2.png",
      weight: "200g",
      price: 565,
      originalPrice: 625,
      rating: 4.3,
      reviews: 9,
      badge: "Healthy Choice",
      badgeColor: "bg-green-500"
    },
    {
      id: 5,
      name: "Happilo Honey Roasted Pistachios",
      image: "/dev1.png",
      weight: "150g",
      price: 525,
      originalPrice: null,
      rating: 4.5,
      reviews: 16,
      badge: "Sweet Treat",
      badgeColor: "bg-yellow-500"
    },
    {
      id: 6,
      name: "Happilo Pistachio Butter Spread",
      image: "/dev2.png",
      weight: "180g",
      price: 385,
      originalPrice: 435,
      rating: 4.2,
      reviews: 7,
      badge: "New Launch",
      badgeColor: "bg-blue-500"
    },
    {
      id: 7,
      name: "Happilo Jumbo Pistachios Gift Box",
      image: "/dev1.png",
      weight: "500g",
      price: 0,
      originalPrice: null,
      rating: null,
      reviews: 0,
      badge: "SOLD OUT",
      badgeColor: "bg-gray-500"
    },
    {
      id: 8,
      name: "Happilo Organic Pistachios",
      image: "/dev2.png",
      weight: "250g",
      price: 0,
      originalPrice: null,
      rating: null,
      reviews: 0,
      badge: "SOLD OUT",
      badgeColor: "bg-gray-500"
    }
  ];

  const sizes = ['150g (3)', '180g (1)', '200g (2)', '250g (2)', '500g (1)', '150g (pack of 2) (1)', '200g (pack of 2) (1)'];

  const renderStars = (rating) => {
    if (!rating) return null;
    return Array.from({ length: 5 }, (_, index) => (
      <span
        key={index}
        className={`text-yellow-400 ${index < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'}`}
      >
        ★
      </span>
    ));
  };

  const handleSizeChange = (size) => {
    setSelectedSizes(prev => 
      prev.includes(size) 
        ? prev.filter(s => s !== size)
        : [...prev, size]
    );
  };

  return (
    <div className="w-full min-h-screen">
      <Header />
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 py-8">
        <div className="flex gap-8">
          {/* Sidebar Filters */}
          <div className="w-64 flex-shrink-0">
            {/* Sort By */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-4">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                </svg>
                <span className="font-medium">Sorted By</span>
              </div>
              <select 
                value={sortBy} 
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded"
              >
                <option>Featured</option>
                <option>Price: Low to High</option>
                <option>Price: High to Low</option>
                <option>Newest</option>
                <option>Customer Rating</option>
              </select>
            </div>

            {/* Price Filter */}
            <div className="mb-6">
              <h3 className="font-medium mb-4">Price</h3>
              <div className="flex items-center gap-2 mb-4">
                <input 
                  type="number" 
                  placeholder="Min"
                  value={priceRange.min}
                  onChange={(e) => setPriceRange(prev => ({ ...prev, min: parseInt(e.target.value) || 0 }))}
                  className="w-16 p-1 border border-gray-300 rounded text-sm"
                />
                <span>to</span>
                <input 
                  type="number" 
                  placeholder="Max"
                  value={priceRange.max}
                  onChange={(e) => setPriceRange(prev => ({ ...prev, max: parseInt(e.target.value) || 800 }))}
                  className="w-16 p-1 border border-gray-300 rounded text-sm"
                />
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: '65%' }}></div>
              </div>
            </div>

            {/* Size Filter */}
            <div className="mb-6">
              <h3 className="font-medium mb-4">Size</h3>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {sizes.map((size, index) => (
                  <label key={index} className="flex items-center gap-2">
                    <input 
                      type="checkbox"
                      checked={selectedSizes.includes(size)}
                      onChange={() => handleSizeChange(size)}
                      className="rounded"
                    />
                    <span className="text-sm text-gray-600">{size}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="flex-1">
            {/* Top bar with sort */}
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold">Pistachios</h1>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Sort by:</span>
                <select 
                  value={sortBy} 
                  onChange={(e) => setSortBy(e.target.value)}
                  className="p-2 border border-gray-300 rounded"
                >
                  <option>Featured</option>
                  <option>Price: Low to High</option>
                  <option>Price: High to Low</option>
                  <option>Newest</option>
                  <option>Customer Rating</option>
                </select>
              </div>
            </div>

            {/* Products Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {products.map((product) => (
                <div key={product.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border border-gray-200">
                  {/* Product Image */}
                  <div className="relative">
                    <div className="w-full h-48 bg-gray-100 flex items-center justify-center relative">
                      {/* Badge */}
                      <div className="absolute top-3 left-3">
                        <span className={`${product.badgeColor} text-white px-2 py-1 text-xs font-semibold rounded`}>
                          {product.badge}
                        </span>
                      </div>
                      
                      {/* Product Image */}
                      <img 
                        src={product.image} 
                        alt={product.name}
                        className="w-24 h-24 object-contain"
                      />
                    </div>
                  </div>

                  {/* Product Details */}
                  <div className="p-4">
                    {/* Product Name */}
                    <h3 className="text-sm font-medium text-gray-800 mb-2 line-clamp-2 h-10">
                      {product.name}
                    </h3>

                    {/* Rating */}
                    {product.rating && (
                      <div className="flex items-center mb-2">
                        <div className="flex mr-1">
                          {renderStars(product.rating)}
                        </div>
                        <span className="text-xs text-gray-600">
                          {product.rating} | {product.reviews} Rating{product.reviews !== 1 ? 's' : ''}
                        </span>
                      </div>
                    )}

                    {/* Weight */}
                    <div className="mb-2">
                      <span className="text-sm font-medium text-gray-800">{product.weight}</span>
                    </div>

                    {/* Price */}
                    <div className="mb-3">
                      {product.price > 0 ? (
                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold text-gray-800">₹ {product.price}</span>
                          {product.originalPrice && (
                            <span className="text-sm text-gray-500 line-through">₹ {product.originalPrice}</span>
                          )}
                        </div>
                      ) : (
                        <div className="text-sm text-gray-600">Price on request</div>
                      )}
                    </div>

                    {/* Action Button */}
                    <button
                      className={`w-full py-2 px-4 rounded font-medium text-sm transition-colors ${
                        product.badge === 'SOLD OUT'
                          ? 'bg-gray-400 text-white cursor-not-allowed'
                          : 'bg-green-700 hover:bg-green-800 text-white'
                      }`}
                      disabled={product.badge === 'SOLD OUT'}
                    >
                      {product.badge === 'SOLD OUT' ? 'Sold Out' : 'Add to Cart'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Product Information Section */}
        <div className="mt-16 space-y-8">
          <div>
            <h2 className="text-3xl font-bold text-gray-800 mb-6">Premium Quality Pistachios Online from Happilo</h2>
            <p className="text-gray-600 leading-relaxed">
              Discover the finest selection of pistachios at Happilo. Our premium pistachios are sourced from the best farms worldwide, including California and Turkey. Known for their distinctive flavor and vibrant green color, these nutrient-packed nuts are perfect for healthy snacking and gourmet cooking.
            </p>
          </div>

          <div>
            <h3 className="text-2xl font-bold text-gray-800 mb-4">What are Pistachios?</h3>
            <p className="text-gray-600 leading-relaxed">
              Pistachios are small, oval-shaped nuts with a hard, beige shell that naturally splits open when ripe. Inside, you'll find the distinctive green and purple kernel with its unique, slightly sweet and nutty flavor. They're one of the few nuts that are naturally colorful and visually appealing.
            </p>
          </div>

          <div>
            <h3 className="text-2xl font-bold text-gray-800 mb-4">Health Benefits of Pistachios</h3>
            <div className="space-y-4 text-gray-600 leading-relaxed">
              <p><strong>Eye Health:</strong> Rich in lutein and zeaxanthin, antioxidants that protect against age-related eye conditions.</p>
              <p><strong>Heart Health:</strong> Contains healthy monounsaturated fats that help reduce cholesterol and support cardiovascular health.</p>
              <p><strong>Weight Management:</strong> High protein and fiber content promote satiety and healthy weight control.</p>
              <p><strong>Blood Sugar Control:</strong> Low glycemic index helps maintain stable blood sugar levels.</p>
              <p><strong>Antioxidant Power:</strong> High in antioxidants including vitamin E, which helps protect cells from damage.</p>
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default Pistachios;
