import { useState } from 'react';
import { Link } from 'react-router-dom';
import { cartAPI } from '../../services/api.js';
import { useNotification } from '../Common/NotificationProvider.jsx';

const YouMayAlsoLike = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { showSuccess, showError } = useNotification();

  const handleAddToCart = (e, product) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (product.isSoldOut) {
      showError('This product is currently sold out');
      return;
    }

    const cartItem = {
      productId: product.id,
      name: product.name,
      size: product.weight,
      price: product.currentPrice,
      quantity: 1,
      image: product.image
    };

    try {
      cartAPI.addToCart(cartItem);
      showSuccess('Product added to cart!');
    } catch (error) {
      console.error('Error adding to cart:', error);
      showError('Failed to add product to cart');
    }
  };

  const products = [
    {
      id: "68d82a64f82a1840340f5dfd",
      name: "Happilo Premium Dried Blueberry",
      image: "/dev1.png", 
      badge: "Must try",
      saleTag: "On sale",
      rating: 4.1,
      totalRatings: 4,
      weight: "200g",
      currentPrice: 309,
      originalPrice: 355,
      backgroundColor: "bg-purple-100"
    },
    {
      id: "68d824fd1ec07a43e1a27cb4",
      name: "Happilo Healthy & Sweet Premium Cranberries",
      image: "/dev2.png", // placeholder
      badge: "Premium",
      saleTag: "On sale",
      rating: 4.5,
      totalRatings: 3,
      weight: "200g",
      currentPrice: 309,
      originalPrice: 370,
      backgroundColor: "bg-red-100"
    },
    {
      id: "68d824fd1ec07a43e1a27cb8",
      name: "Happilo Healthy & Sweet California Blueberries",
      image: "/dev1.png", // placeholder
      badge: "Must try",
      saleTag: "Sold out",
      rating: 5.0,
      totalRatings: 1,
      weight: "200g",
      originalPrice: 396,
      isSoldOut: true,
      backgroundColor: "bg-blue-100"
    },
    {
      id: "68d824fd1ec07a43e1a27ca0",
      name: "Happilo 100% Natural Himalayan Dried Goji Berries",
      image: "/dev2.png", // placeholder
      badge: "Premium",
      saleTag: "Sold out",
      rating: 4.0,
      totalRatings: 2,
      mrp: 266,
      isSoldOut: true,
      backgroundColor: "bg-pink-100"
    },
    {
      id: "68d824fd1ec07a43e1a27ca8",
      name: "Gameful Corn Nut Combo 160g (Traditional & Tangy)",
      image: "/dev1.png", // placeholder
      saleTag: "Sold out",
      mrp: 0,
      isSoldOut: true,
      backgroundColor: "bg-yellow-100"
    }
  ];

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex + 1 >= products.length - 3 ? 0 : prevIndex + 1
    );
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? Math.max(0, products.length - 4) : prevIndex - 1
    );
  };

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span
        key={index}
        className={`text-yellow-400 ${index < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'}`}
      >
        ★
      </span>
    ));
  };

  return (
    <div className="w-full bg-white py-[8vh] md:py-[10vh]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Section Title */}
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-center mb-8 md:mb-12 text-gray-800">
          You May Also Like
        </h2>

        {/* Products Carousel Container */}
        <div className="relative">
          {/* Navigation Arrows */}
          <button
            onClick={prevSlide}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white shadow-lg rounded-full p-2 hover:bg-gray-50 transition-colors"
            style={{ marginLeft: '-20px' }}
          >
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white shadow-lg rounded-full p-2 hover:bg-gray-50 transition-colors"
            style={{ marginRight: '-20px' }}
          >
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>

          {/* Products Grid */}
          <div className="overflow-hidden">
            <div 
              className="flex transition-transform duration-300 ease-in-out"
              style={{ transform: `translateX(-${currentIndex * (100 / 4)}%)` }}
            >
              {products.map((product) => (
                <div
                  key={product.id}
                  className="flex-shrink-0 w-full md:w-1/2 lg:w-1/3 xl:w-1/4 px-2"
                >
                  <Link 
                    to={`/product/${product.id}`}
                    className="block bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden border border-gray-200"
                  >
                    {/* Product Image */}
                    <div className="relative">
                      <div className={`w-full h-48 ${product.backgroundColor} flex items-center justify-center relative`}>
                        {/* Badges */}
                        <div className="absolute top-3 left-3 flex flex-col gap-1">
                          {product.badge && (
                            <span className={`px-2 py-1 text-xs font-semibold rounded text-white ${
                              product.badge === 'Must try' ? 'bg-red-500' : 'bg-orange-500'
                            }`}>
                              {product.badge}
                            </span>
                          )}
                          {product.saleTag && (
                            <span className={`px-2 py-1 text-xs font-semibold rounded ${
                              product.saleTag === 'On sale' ? 'bg-green-100 text-green-800' : 
                              product.saleTag === 'Sold out' ? 'bg-gray-100 text-gray-800' : ''
                            }`}>
                              {product.saleTag}
                            </span>
                          )}
                        </div>
                        
                        {/* Product Image */}
                        <img 
                          src={product.image} 
                          alt={product.name}
                          className="w-24 h-24 object-contain"
                        />
                      </div>
                    </div>

                    {/* Product Details */}
                    <div className="p-4">
                      {/* Product Name */}
                      <h3 className="text-sm font-medium text-gray-800 mb-2 line-clamp-2 h-10">
                        {product.name}
                      </h3>

                      {/* Rating */}
                      {product.rating && (
                        <div className="flex items-center mb-2">
                          <div className="flex mr-1">
                            {renderStars(product.rating)}
                          </div>
                          <span className="text-xs text-gray-600">
                            {product.rating} | {product.totalRatings} Rating{product.totalRatings !== 1 ? 's' : ''}
                          </span>
                        </div>
                      )}

                      {/* Weight */}
                      {product.weight && (
                        <div className="mb-2">
                          <span className="text-sm font-medium text-gray-800">{product.weight}</span>
                        </div>
                      )}

                      {/* Price */}
                      <div className="mb-3">
                        {product.currentPrice ? (
                          <div className="flex items-center gap-2">
                            <span className="text-lg font-bold text-gray-800">₹ {product.currentPrice}</span>
                            {product.originalPrice && (
                              <span className="text-sm text-gray-500 line-through">MRP ₹ {product.originalPrice}</span>
                            )}
                          </div>
                        ) : product.mrp !== undefined ? (
                          <div className="text-sm text-gray-600">
                            {product.mrp > 0 ? `MRP ₹ ${product.mrp}` : 'MRP ₹ 0'}
                          </div>
                        ) : null}
                      </div>

                    </div>
                  </Link>

                  {/* Action Button - Outside Link */}
                  <div className="px-4 pb-4">
                    <button
                      onClick={(e) => handleAddToCart(e, product)}
                      className={`w-full py-2 px-4 rounded font-medium text-sm transition-colors ${
                        product.isSoldOut
                          ? 'bg-green-700 text-white cursor-not-allowed'
                          : 'bg-green-700 hover:bg-green-800 text-white'
                      }`}
                      disabled={product.isSoldOut}
                    >
                      {product.isSoldOut ? 'Sold Out' : 'Add to Cart'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default YouMayAlsoLike;
