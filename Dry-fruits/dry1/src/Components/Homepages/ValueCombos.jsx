import { FaStar } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import { cartAPI } from '../../services/api.js';
import { useNotification } from '../Common/NotificationProvider.jsx';

const ValueCombos = () => {
  const { showSuccess, showError } = useNotification();

  const handleAddToCart = (e, product) => {
    e.preventDefault();
    e.stopPropagation();

    const cartItem = {
      productId: product.id,
      name: product.title,
      size: 'Combo',
      price: product.price,
      quantity: 1,
      image: product.image
    };

    try {
      cartAPI.addToCart(cartItem);
      showSuccess('Combo added to cart!');
    } catch (error) {
      console.error('Error adding to cart:', error);
      showError('Failed to add combo to cart');
    }
  };

  const products = [
    {
      id: "68d82a64f82a1840340f5dfd",
      title: "Premium Nuts & Dry Fruits Combo ...",
      image: "", // Placeholder for product image
      rating: 5,
      totalRatings: "5 | Ratings",
      price: 729,
      mrp: "MRP ₹ 729",
      badge: "Value Pack",
      badgeColor: "bg-red-500",
      soldOut: false,
      buttonText: "Add to Cart"
    },
    {
      id: "68d824fd1ec07a43e1a27cb4",
      title: "Premium Dry Fruits Combo 1050g (...",
      image: "", // Placeholder for product image
      rating: 5,
      totalRatings: "2 | Ratings",
      price: 1367,
      mrp: "MRP ₹ 1,367",
      badge: "Sold out",
      badgeColor: "bg-gray-500",
      soldOut: true,
      buttonText: "Add to Cart"
    },
    {
      id: "68d824fd1ec07a43e1a27cb8",
      title: "Happilo Premium Californian Almo...",
      image: "", // Placeholder for product image
      rating: 0,
      totalRatings: "",
      price: 1095,
      mrp: "MRP ₹ 1,095",
      badge: "Sold out",
      badgeColor: "bg-gray-500",
      soldOut: true,
      buttonText: "Add to Cart"
    },
    {
      id: "68d824fd1ec07a43e1a27ca0",
      title: "Premium Dry Fruits Combo (Califo...",
      image: "", // Placeholder for product image
      rating: 0,
      totalRatings: "",
      price: 923,
      mrp: "MRP ₹ 923",
      badge: "Value Pack",
      badgeColor: "bg-red-500",
      soldOut: false,
      buttonText: "Add to Cart"
    },
    {
      id: "68d824fd1ec07a43e1a27ca8",
      title: "Happilo Premium Nuts & Dry Fruit...",
      image: "", // Placeholder for product image
      rating: 0,
      totalRatings: "",
      price: 1236,
      mrp: "MRP ₹ 1,236",
      badge: "Sold out",
      badgeColor: "bg-gray-500",
      soldOut: true,
      buttonText: "Add to Cart"
    },
    {
      id: "68d824fd1ec07a43e1a27cac",
      title: "Premium Dry Fruits Combo (Califo...",
      image: "", // Placeholder for product image
      rating: 5,
      totalRatings: "1 | Rating",
      price: 779,
      mrp: "MRP ₹ 779",
      badge: "Value Pack",
      badgeColor: "bg-red-500",
      soldOut: false,
      buttonText: "Add to Cart"
    },
    {
      id: "68d824fd1ec07a43e1a27cb0",
      title: "Dry Fruits Combo 850g (Almonds 2...",
      image: "", // Placeholder for product image
      rating: 5,
      totalRatings: "11 | Ratings",
      price: 858,
      mrp: "MRP ₹ 858",
      badge: "Value Pack",
      badgeColor: "bg-red-500",
      soldOut: false,
      buttonText: "Add to Cart"
    },
    {
      id: "68d824fd1ec07a43e1a27cb2",
      title: "Happilo Popular Nuts Combo 800g ...",
      image: "", // Placeholder for product image
      rating: 0,
      totalRatings: "",
      price: 1300,
      mrp: "MRP ₹ 1,300",
      badge: "Sold out",
      badgeColor: "bg-gray-500",
      soldOut: true,
      buttonText: "Add to Cart"
    }
  ];

  return (
    <div className="w-full bg-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Section Title */}
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-center mb-8 md:mb-12 text-gray-800">
          Value Combos
        </h2>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
          {products.map((product) => (
            <div
              key={product.id}
              className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border relative"
            >
              {/* Clickable Product Area */}
              <Link 
                to={`/product/${product.id}`}
                className="block"
              >
              {/* Badge */}
              <div className="absolute top-3 left-3 z-10">
                <span className={`${product.badgeColor} text-white px-2 py-1 text-xs font-semibold rounded`}>
                  {product.badge}
                </span>
              </div>

              {/* Sold Out Tag */}
              <div className="absolute top-3 right-3 z-10">
                <span className="bg-gray-500 text-white px-2 py-1 text-xs rounded">
                  Sold out
                </span>
              </div>

              {/* Product Image */}
              <div className="aspect-[4/3] bg-gray-100 flex items-center justify-center relative overflow-hidden">
                {/* Placeholder for product image */}
                <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                  <span className="text-gray-400 text-sm">Combo Package</span>
                </div>
              </div>

              {/* Product Info */}
              <div className="p-4">
                {/* Product Title */}
                <h3 className="text-sm font-medium text-gray-800 mb-3 line-clamp-2 leading-tight">
                  {product.title}
                </h3>

                {/* Rating */}
                {product.totalRatings && (
                  <>
                    <div className="flex items-center mb-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, index) => (
                          <FaStar
                            key={index}
                            className={`w-3 h-3 ${
                              index < product.rating
                                ? 'text-yellow-400'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>

                    {/* Ratings Count */}
                    <p className="text-xs text-gray-500 mb-3">{product.totalRatings}</p>
                  </>
                )}

                {/* MRP */}
                <div className="mb-4">
                  <span className="text-sm text-gray-500">{product.mrp}</span>
                </div>
              </div>
              </Link>

              {/* Add to Cart Button - Outside Link */}
              <div className="px-4 pb-4">
                <button 
                  onClick={(e) => handleAddToCart(e, product)}
                  className="w-full bg-green-700 text-white py-2 rounded font-semibold text-sm hover:bg-green-800 transition-colors"
                >
                  {product.buttonText}
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ValueCombos;
