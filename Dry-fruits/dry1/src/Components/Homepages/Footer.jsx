import React, { useState } from 'react';

const Footer = () => {
  const [whatsappNumber, setWhatsappNumber] = useState('');

  const handleWhatsAppSubmit = (e) => {
    e.preventDefault();
    if (whatsappNumber) {
      // Handle WhatsApp subscription logic here
      console.log('WhatsApp number submitted:', whatsappNumber);
      setWhatsappNumber('');
    }
  };

  return (
    <footer className="bg-green-600 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 py-12 md:py-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 md:gap-12">
          
          {/* Track My Order Section */}
          <div className="lg:col-span-1">
            <h3 className="text-lg font-semibold mb-4">Track My Order</h3>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-green-100 hover:text-white transition-colors duration-200">
                  Where is my order?
                </a>
              </li>
            </ul>
          </div>

          {/* Terms & Policies Section */}
          <div className="lg:col-span-1">
            <h3 className="text-lg font-semibold mb-4">Terms & Policies</h3>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-green-100 hover:text-white transition-colors duration-200">
                  Terms of Services
                </a>
              </li>
              <li>
                <a href="#" className="text-green-100 hover:text-white transition-colors duration-200">
                  Privacy Policy
                </a>
              </li>
              <li>
                <a href="#" className="text-green-100 hover:text-white transition-colors duration-200">
                  Shipping Policy
                </a>
              </li>
              <li>
                <a href="#" className="text-green-100 hover:text-white transition-colors duration-200">
                  Return & Refund policy
                </a>
              </li>
            </ul>
          </div>

          {/* Updates Section */}
          <div className="lg:col-span-1">
            <h3 className="text-lg font-semibold mb-4">Updates</h3>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-green-100 hover:text-white transition-colors duration-200">
                  Blogs & Articles
                </a>
              </li>
              <li>
                <a href="#" className="text-green-100 hover:text-white transition-colors duration-200">
                  Careers
                </a>
              </li>
            </ul>
          </div>

          {/* Help Section */}
          <div className="lg:col-span-1">
            <h3 className="text-lg font-semibold mb-4">Help</h3>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-green-100 hover:text-white transition-colors duration-200">
                  My Account
                </a>
              </li>
              <li>
                <a href="#" className="text-green-100 hover:text-white transition-colors duration-200">
                  Track your Order
                </a>
              </li>
              <li>
                <a href="#" className="text-green-100 hover:text-white transition-colors duration-200">
                  Contact Us
                </a>
              </li>
            </ul>
          </div>

          {/* About Section */}
          <div className="lg:col-span-1">
            <h3 className="text-lg font-semibold mb-4">About</h3>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-green-100 hover:text-white transition-colors duration-200">
                  About Happilo!
                </a>
              </li>
              <li>
                <a href="#" className="text-green-100 hover:text-white transition-colors duration-200">
                  Our Vision
                </a>
              </li>
              <li>
                <a href="#" className="text-green-100 hover:text-white transition-colors duration-200">
                  Why We Exist
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* WhatsApp Newsletter Section */}
        <div className="mt-12 text-center">
          <p className="text-green-100 mb-4">Don't miss out on the latest offers</p>
          <form onSubmit={handleWhatsAppSubmit} className="flex flex-col sm:flex-row items-center justify-center gap-3 max-w-md mx-auto">
            <div className="relative flex-1 w-full">
              <input
                type="tel"
                value={whatsappNumber}
                onChange={(e) => setWhatsappNumber(e.target.value)}
                placeholder="Enter your WhatsApp number"
                className="w-full px-4 py-3 pr-12 rounded-lg text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-400"
              />
              <button
                type="submit"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-green-500 hover:bg-green-600 p-2 rounded-md transition-colors duration-200"
              >
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.525 3.297"/>
                </svg>
              </button>
            </div>
          </form>
        </div>

        {/* Payment Methods Section */}
        <div className="mt-12 text-center">
          <p className="text-green-100 mb-6">We Accept</p>
          <div className="flex flex-wrap items-center justify-center gap-6 md:gap-8">
            {/* Google Pay */}
            <div className="bg-white rounded-lg px-4 py-2">
              <span className="text-gray-800 font-semibold text-lg">G Pay</span>
            </div>
            
            {/* Amazon Pay */}
            <div className="bg-white rounded-lg px-4 py-2">
              <span className="text-gray-800 font-semibold text-lg">amazon pay</span>
            </div>
            
            {/* Visa */}
            <div className="bg-white rounded-lg px-4 py-2">
              <span className="text-blue-600 font-bold text-lg">VISA</span>
            </div>
            
            {/* Mastercard */}
            <div className="bg-white rounded-lg px-4 py-2 flex items-center">
              <div className="w-6 h-6 bg-red-500 rounded-full mr-1"></div>
              <div className="w-6 h-6 bg-yellow-500 rounded-full -ml-3"></div>
            </div>
            
            {/* BHIM UPI */}
            <div className="bg-white rounded-lg px-4 py-2">
              <span className="text-gray-800 font-semibold text-lg">BHIM UPI</span>
            </div>
            
            {/* RuPay */}
            <div className="bg-white rounded-lg px-4 py-2">
              <span className="text-green-600 font-semibold text-lg">RuPay</span>
            </div>
          </div>
        </div>

        {/* Social Media Section */}
        <div className="mt-12 text-center">
          <p className="text-green-100 mb-6">Follow Us!</p>
          <div className="flex items-center justify-center gap-4">
            {/* Facebook */}
            <a href="#" className="bg-white text-blue-600 p-3 rounded-full hover:bg-gray-100 transition-colors duration-200">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </a>
            
            {/* Twitter */}
            <a href="#" className="bg-white text-blue-400 p-3 rounded-full hover:bg-gray-100 transition-colors duration-200">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
            </a>
            
            {/* Instagram */}
            <a href="#" className="bg-white text-pink-600 p-3 rounded-full hover:bg-gray-100 transition-colors duration-200">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.348-1.051-2.348-2.348 0-1.297 1.051-2.348 2.348-2.348 1.297 0 2.348 1.051 2.348 2.348 0 1.297-1.051 2.348-2.348 2.348zm7.718 0c-1.297 0-2.348-1.051-2.348-2.348 0-1.297 1.051-2.348 2.348-2.348 1.297 0 2.348 1.051 2.348 2.348 0 1.297-1.051 2.348-2.348 2.348z"/>
              </svg>
            </a>
            
            {/* LinkedIn */}
            <a href="#" className="bg-white text-blue-700 p-3 rounded-full hover:bg-gray-100 transition-colors duration-200">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </a>
            
            {/* YouTube */}
            <a href="#" className="bg-white text-red-600 p-3 rounded-full hover:bg-gray-100 transition-colors duration-200">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
            </a>
          </div>
        </div>

        {/* Copyright Section */}
        <div className="mt-12 pt-8 border-t border-green-500 text-center text-green-100">
          <p>&copy; 2024 Happilo. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
