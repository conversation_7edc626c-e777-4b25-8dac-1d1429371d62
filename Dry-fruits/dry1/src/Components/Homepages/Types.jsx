const Types = () => {
  return (
    <div className="w-full bg-black text-white py-3">
      <div className="flex items-center justify-center space-x-8 text-sm font-medium">
        <a href="#" className="hover:text-gray-300 transition-colors">Shop</a>
        <span className="text-gray-400">•</span>
        <a href="#" className="hover:text-gray-300 transition-colors flex items-center">
          🎁 Gifting
        </a>
        <span className="text-gray-400">•</span>
        <a href="#" className="hover:text-gray-300 transition-colors flex items-center">
          ⭐ Jumbo Nuts
        </a>
        <span className="text-gray-400">•</span>
        <a href="#" className="hover:text-gray-300 transition-colors flex items-center">
          🔔 Snacking
        </a>
        <span className="text-gray-400">•</span>
        <a href="#" className="hover:text-gray-300 transition-colors flex items-center">
          🍫 Combos
        </a>
        <span className="text-gray-400">•</span>
        <a href="#" className="hover:text-gray-300 transition-colors flex items-center">
          🌱 Seeds
        </a>
        <span className="text-gray-400">•</span>
        <a href="#" className="hover:text-gray-300 transition-colors flex items-center">
          🍓 Berries
        </a>
        <span className="text-gray-400">•</span>
        <a href="#" className="hover:text-gray-300 transition-colors flex items-center">
          🥭 Exotic Nuts
        </a>
        <span className="text-gray-400">•</span>
        <a href="#" className="hover:text-gray-300 transition-colors flex items-center">
          🌿 Spices
        </a>
        <span className="text-gray-400">•</span>
        <a href="#" className="hover:text-gray-300 transition-colors flex items-center">
          🛍️ Bulk Shop
        </a>
      </div>
    </div>
  )
}

export default Types
