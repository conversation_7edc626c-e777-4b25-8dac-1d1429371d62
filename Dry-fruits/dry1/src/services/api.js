import config from '../config/environment.js';
import { URLs } from '../utils/urls.js';

const API_BASE_URL = config.API_BASE_URL;

// Auth token management
const getAuthToken = () => {
  return localStorage.getItem('authToken');
};

const setAuthToken = (token) => {
  localStorage.setItem('authToken', token);
};

const removeAuthToken = () => {
  localStorage.removeItem('authToken');
};

// Admin token management
const getAdminToken = () => {
  return localStorage.getItem('adminToken');
};

const setAdminToken = (token) => {
  localStorage.setItem('adminToken', token);
};

const removeAdminToken = () => {
  localStorage.removeItem('adminToken');
};

// HTTP request helper
const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const token = getAuthToken();
  
  const requestConfig = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  // Add timeout for requests
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), config.REQUEST_TIMEOUT || 10000);
  requestConfig.signal = controller.signal;

  try {
    if (config.IS_DEVELOPMENT && config.ENABLE_DEBUG) {
      console.log(`🌐 API Request: ${requestConfig.method || 'GET'} ${url}`);
      console.log(`🔧 API_BASE_URL: ${API_BASE_URL}`);
    }

    const response = await fetch(url, requestConfig);
    clearTimeout(timeoutId);

    // Handle different response types
    let data;
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    if (!response.ok) {
      const errorMessage = data?.message || data || `HTTP ${response.status}: ${response.statusText}`;
      
      if (config.IS_DEVELOPMENT) {
        console.error('❌ API Error:', {
          status: response.status,
          statusText: response.statusText,
          url,
          data
        });
      }
      
      throw new Error(errorMessage);
    }

    if (config.IS_DEVELOPMENT && config.ENABLE_DEBUG) {
      console.log(`✅ API Response: ${requestConfig.method || 'GET'} ${url}`, data);
    }

    return data;
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error.name === 'AbortError') {
      throw new Error('Request timeout - please try again');
    }
    
    if (config.IS_DEVELOPMENT) {
      console.error('❌ API Request Error:', {
        url,
        error: error.message,
        stack: error.stack
      });
    }
    
    throw error;
  }
};

// Admin HTTP request helper
const adminApiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const adminToken = getAdminToken();
  
  const requestConfig = {
    headers: {
      'Content-Type': 'application/json',
      ...(adminToken && { Authorization: `Bearer ${adminToken}` }),
      ...options.headers,
    },
    ...options,
  };

  // Add timeout for requests
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), config.REQUEST_TIMEOUT || 10000);
  requestConfig.signal = controller.signal;

  try {
    if (config.IS_DEVELOPMENT && config.ENABLE_DEBUG) {
      console.log(`🌐 Admin API Request: ${requestConfig.method || 'GET'} ${url}`);
      console.log(`🔧 API_BASE_URL: ${API_BASE_URL}`);
    }

    const response = await fetch(url, requestConfig);
    clearTimeout(timeoutId);

    // Handle different response types
    let data;
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    if (!response.ok) {
      const error = new Error(data.message || `HTTP error! status: ${response.status}`);
      error.status = response.status;
      error.statusText = response.statusText;
      error.url = url;
      error.data = data;
      throw error;
    }

    if (config.IS_DEVELOPMENT && config.ENABLE_DEBUG) {
      console.log(`✅ Admin API Response: ${requestConfig.method || 'GET'} ${url}`, data);
    }

    return data;
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error.name === 'AbortError') {
      const timeoutError = new Error('Request timeout');
      timeoutError.status = 408;
      timeoutError.statusText = 'Request Timeout';
      timeoutError.url = url;
      throw timeoutError;
    }

    if (config.IS_DEVELOPMENT && config.ENABLE_DEBUG) {
      console.error(`❌ Admin API Error:`, {
        status: error.status,
        statusText: error.statusText,
        url: error.url,
        data: error.data
      });
    }
    
    throw error;
  }
};

// Auth API
export const authAPI = {
  register: (userData) => apiRequest('/auth/register', {
    method: 'POST',
    body: JSON.stringify(userData),
  }),

  login: async (credentials) => {
    const data = await apiRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    if (data.token) {
      setAuthToken(data.token);
    }
    return data;
  },

  logout: () => {
    removeAuthToken();
  },

  getProfile: () => apiRequest('/auth/profile'),

  updateProfile: (profileData) => apiRequest('/auth/profile', {
    method: 'PUT',
    body: JSON.stringify(profileData),
  }),

  changePassword: (passwordData) => apiRequest('/auth/change-password', {
    method: 'POST',
    body: JSON.stringify(passwordData),
  }),

  verifyToken: () => apiRequest('/auth/verify-token', {
    method: 'POST',
  }),

  // Admin login
  adminLogin: async (credentials) => {
    const data = await apiRequest('/auth/admin-login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    if (data.token) {
      setAdminToken(data.token);
    }
    return data;
  },

  // Admin profile
  getAdminProfile: () => adminApiRequest('/auth/admin-profile'),

  // Admin logout
  adminLogout: () => {
    removeAdminToken();
  },
};

// Products API
export const productsAPI = {
  getAll: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/products${queryString ? `?${queryString}` : ''}`);
  },

  getById: (id) => apiRequest(`/products/${id}`),

  getFeatured: () => apiRequest('/products/featured'),

  getCategories: () => apiRequest('/products/categories'),

  search: (query, limit = 10) => apiRequest(`/products/search?q=${encodeURIComponent(query)}&limit=${limit}`),

  create: (productData) => adminApiRequest('/products', {
    method: 'POST',
    body: JSON.stringify(productData),
  }),

  update: (id, productData) => adminApiRequest(`/products/${id}`, {
    method: 'PUT',
    body: JSON.stringify(productData),
  }),

  delete: (id) => adminApiRequest(`/products/${id}`, {
    method: 'DELETE',
  }),

  toggleWishlist: (id) => apiRequest(`/products/${id}/wishlist`, {
    method: 'POST',
  }),
};

// Reviews API
export const reviewsAPI = {
  getByProduct: (productId, params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/reviews/product/${productId}${queryString ? `?${queryString}` : ''}`);
  },

  getAll: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/reviews${queryString ? `?${queryString}` : ''}`);
  },

  create: (reviewData) => apiRequest('/reviews', {
    method: 'POST',
    body: JSON.stringify(reviewData),
  }),

  updateStatus: (id, statusData) => apiRequest(`/reviews/${id}/status`, {
    method: 'PUT',
    body: JSON.stringify(statusData),
  }),

  update: (id, reviewData) => adminApiRequest(`/reviews/${id}`, {
    method: 'PUT',
    body: JSON.stringify(reviewData),
  }),

  delete: (id) => adminApiRequest(`/reviews/${id}`, {
    method: 'DELETE',
  }),

  getStats: () => adminApiRequest('/reviews/stats'),
};

// Orders API
export const ordersAPI = {
  getAll: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return adminApiRequest(`/orders${queryString ? `?${queryString}` : ''}`);
  },

  getById: (id) => adminApiRequest(`/orders/${id}`),

  create: (orderData) => apiRequest('/orders', {
    method: 'POST',
    body: JSON.stringify(orderData),
  }),

  createDraft: (orderData) => apiRequest('/orders/draft', {
    method: 'POST',
    body: JSON.stringify(orderData),
  }),

  updateStatus: (id, statusData) => adminApiRequest(`/orders/${id}/status`, {
    method: 'PUT',
    body: JSON.stringify(statusData),
  }),

  cancel: (id, reason) => adminApiRequest(`/orders/${id}/cancel`, {
    method: 'POST',
    body: JSON.stringify({ reason }),
  }),

  getStats: () => adminApiRequest('/orders/stats/summary'),

  // Track orders by mobile number (public endpoint)
  trackByMobile: (mobileNumber, params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/orders/track/${mobileNumber}${queryString ? `?${queryString}` : ''}`);
  },
};

// Admin API
export const adminAPI = {
  getDashboard: () => adminApiRequest('/admin/dashboard'),

  getUsers: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return adminApiRequest(`/admin/users${queryString ? `?${queryString}` : ''}`);
  },

  updateUserStatus: (id, statusData) => adminApiRequest(`/admin/users/${id}/status`, {
    method: 'PUT',
    body: JSON.stringify(statusData),
  }),

  getAnalytics: (period = '30') => adminApiRequest(`/admin/analytics?period=${period}`),

  getInventory: () => adminApiRequest('/admin/inventory'),

  bulkUpdateProducts: (updateData) => adminApiRequest('/admin/bulk-update-products', {
    method: 'POST',
    body: JSON.stringify(updateData),
  }),
};

// Manager API
export const managerAPI = {
  getCategories: () => apiRequest('/manager/categories'),
  getProducts: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/manager/products${queryString ? `?${queryString}` : ''}`);
  },
  getProduct: (id) => apiRequest(`/manager/products/${id}`),
  createProduct: (productData) => apiRequest('/manager/products', {
    method: 'POST',
    body: JSON.stringify(productData)
  }),
  updateProduct: (id, productData) => apiRequest(`/manager/products/${id}`, {
    method: 'PUT',
    body: JSON.stringify(productData)
  }),
  deleteProduct: (id) => apiRequest(`/manager/products/${id}`, {
    method: 'DELETE'
  }),
  getProfile: () => apiRequest('/manager/profile')
};

// Cart API (localStorage-based for now, can be moved to backend later)
export const cartAPI = {
  getCart: () => {
    try {
      const cart = localStorage.getItem('cart');
      if (!cart) {
        return [];
      }
      const parsedCart = JSON.parse(cart);
      return Array.isArray(parsedCart) ? parsedCart : [];
    } catch (error) {
      console.error('Error parsing cart from localStorage:', error);
      // Clear invalid cart data
      localStorage.removeItem('cart');
      return [];
    }
  },

  addToCart: (item) => {
    const cart = cartAPI.getCart();
    
    // Ensure price is a number
    const price = parseFloat(item.price) || 0;
    const quantity = parseInt(item.quantity) || 1;
    
    const existingItemIndex = cart.findIndex(
      cartItem => cartItem.productId === item.productId && cartItem.size === item.size
    );

    if (existingItemIndex > -1) {
      cart[existingItemIndex].quantity += quantity;
    } else {
      cart.push({
        id: Date.now().toString(),
        productId: item.productId,
        name: item.name,
        size: item.size,
        price: price,
        quantity: quantity,
        image: item.image,
      });
    }

    localStorage.setItem('cart', JSON.stringify(cart));
    cartAPI.notifyCartUpdate();
    return cart;
  },

  updateQuantity: (itemId, quantity) => {
    const cart = cartAPI.getCart();
    const itemIndex = cart.findIndex(item => item.id === itemId);
    
    if (itemIndex > -1) {
      if (quantity <= 0) {
        cart.splice(itemIndex, 1);
      } else {
        cart[itemIndex].quantity = quantity;
      }
    }

    localStorage.setItem('cart', JSON.stringify(cart));
    cartAPI.notifyCartUpdate();
    return cart;
  },

  removeFromCart: (itemId) => {
    const cart = cartAPI.getCart();
    const updatedCart = cart.filter(item => item.id !== itemId);
    localStorage.setItem('cart', JSON.stringify(updatedCart));
    cartAPI.notifyCartUpdate();
    return updatedCart;
  },

  clearCart: () => {
    localStorage.removeItem('cart');
    cartAPI.notifyCartUpdate();
    return [];
  },

  getCartTotal: () => {
    const cart = cartAPI.getCart();
    return cart.reduce((total, item) => {
      const price = parseFloat(item.price) || 0;
      const quantity = parseInt(item.quantity) || 0;
      return total + (price * quantity);
    }, 0);
  },

  getCartCount: () => {
    const cart = cartAPI.getCart();
    if (!Array.isArray(cart)) {
      return 0;
    }
    return cart.reduce((count, item) => {
      const quantity = parseInt(item.quantity) || 0;
      return count + quantity;
    }, 0);
  },

  notifyCartUpdate: () => {
    // Dispatch custom event to notify components of cart changes
    window.dispatchEvent(new CustomEvent('cartUpdated'));
  },
};

// Error handler helper
export const handleApiError = (error) => {
  if (error.message.includes('401') || error.message.includes('Unauthorized')) {
    removeAuthToken();
    window.location.href = '/login';
  }
  return error.message;
};

// Categories API
const categoriesAPI = {
  getAll: () => apiRequest('/categories'),
  getById: (id) => apiRequest(`/categories/${id}`),
  getByParent: (parentCategory) => apiRequest(`/categories?parentCategory=${parentCategory}`),
};

const uploadAPI = {
  uploadImage: (formData) => {
    const adminToken = getAdminToken();
    return fetch(URLs.UPLOAD.IMAGE, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
      },
      body: formData
    }).then(response => response.json());
  },
  uploadImages: (formData) => {
    const adminToken = getAdminToken();
    return fetch(URLs.UPLOAD.IMAGES, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
      },
      body: formData
    }).then(response => response.json());
  },
  deleteImage: (filename) => adminApiRequest(`/upload/image/${filename}`, { method: 'DELETE' })
};

export default {
  authAPI,
  productsAPI,
  categoriesAPI,
  reviewsAPI,
  ordersAPI,
  adminAPI,
  managerAPI,
  cartAPI,
  uploadAPI,
  handleApiError,
};
