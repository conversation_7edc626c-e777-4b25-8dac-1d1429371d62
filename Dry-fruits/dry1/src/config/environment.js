// Environment configuration
let config = {
  // Default fallback configuration
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5001/api',
  BACKEND_URL: import.meta.env.VITE_BACKEND_URL || 'http://localhost:5001',
  FRONTEND_URL: import.meta.env.VITE_FRONTEND_URL || 'http://localhost:5173',
  WEBSITE_URL: import.meta.env.VITE_WEBSITE_URL || 'http://localhost:5173',
  ADMIN_URL: import.meta.env.VITE_ADMIN_URL || 'http://localhost:5173/admin',
  API_URL: import.meta.env.VITE_API_URL || 'http://localhost:5001/api',
  
  APP_NAME: import.meta.env.VITE_APP_NAME || 'Happilo',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  NODE_ENV: import.meta.env.VITE_NODE_ENV || 'development',
  IS_DEVELOPMENT: import.meta.env.DEV,
  IS_PRODUCTION: import.meta.env.PROD,
  ENABLE_ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
  ENABLE_DEBUG: import.meta.env.VITE_ENABLE_DEBUG === 'true',
  GOOGLE_ANALYTICS_ID: import.meta.env.VITE_GOOGLE_ANALYTICS_ID,
  STRIPE_PUBLISHABLE_KEY: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY,
  MAX_FILE_SIZE: parseInt(import.meta.env.VITE_MAX_FILE_SIZE) || 5000000,
  DEFAULT_PAGE_SIZE: parseInt(import.meta.env.VITE_DEFAULT_PAGE_SIZE) || 12,
  MAX_PAGE_SIZE: parseInt(import.meta.env.VITE_MAX_PAGE_SIZE) || 100,
  CACHE_DURATION: parseInt(import.meta.env.VITE_CACHE_DURATION) || 300000,
  REQUEST_TIMEOUT: parseInt(import.meta.env.VITE_REQUEST_TIMEOUT) || 10000,
};

// Function to fetch configuration from backend
const fetchConfigFromBackend = async () => {
  try {
    const response = await fetch(`${config.API_BASE_URL}/config`);
    if (response.ok) {
      const data = await response.json();
      if (data.success && data.config) {
        // Merge backend config with frontend config
        config = { ...config, ...data.config };
        console.log('🔧 Configuration loaded from backend:', config);
        return true;
      }
    }
  } catch (error) {
    console.warn('⚠️ Failed to load configuration from backend, using fallback:', error.message);
  }
  return false;
};

// Initialize configuration
let configInitialized = false;
const initializeConfig = async () => {
  if (!configInitialized) {
    configInitialized = true;
    await fetchConfigFromBackend();
  }
  return config;
};

// Function to refresh configuration (for admin panel)
const refreshConfig = async () => {
  const success = await fetchConfigFromBackend();
  if (success) {
    validateConfig();
    return config;
  }
  throw new Error('Failed to refresh configuration from backend');
};

// Validation
const validateConfig = () => {
  const requiredVars = ['API_BASE_URL'];
  const missing = requiredVars.filter(key => !config[key]);

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:', missing);
    console.error('Please check your .env file');
  }

  if (config.IS_DEVELOPMENT) {
    console.log('🔧 Development Configuration:', {
      API_BASE_URL: config.API_BASE_URL,
      APP_NAME: config.APP_NAME,
      APP_VERSION: config.APP_VERSION
    });
  }
};

// Run validation
validateConfig();

// Export both the config object and the initialization functions
export { initializeConfig, refreshConfig };
export default config;
